//@ts-nocheck

import { z } from "zod";
import { sql } from "drizzle-orm";
import { randomUUID } from "node:crypto";
import { assignments } from "./assignments";
import { sqliteTable, text } from "drizzle-orm/sqlite-core";

// submissions
export const submissions = sqliteTable("submissions", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => randomUUID()),

  student_id: text("student_id").default(null),
  // writing
  story_text: text("story_text").default(""),
  assignment_id: text("assignment_id").references(() => assignments.id),

  // brainstorm and ideate
  theme: text("theme").default("").notNull(),
  setting: text("setting").default("").notNull(),
  key_plot_points: text("key_plot_points").default("").notNull(),

  // story-starters
  story_starters: text("story_starters", { mode: "json" }).default([]),

  // sentence-starters
  plot_structure_scaffold: text("plot_structure_scaffold", {
    mode: "json",
  }).default([]),
  // narrative-checklist
  narrative_checklist: text("narrative_checklist", {
    mode: "json",
  }).default([]),

  // sentence-challenges
  sentence_challenges: text("sentence_challenges", {
    mode: "json",
  }).default([]),

  // thought-experiments
  thought_experiments: text("thought_experiments", {
    mode: "json",
  }).default([]),

  // reflections
  reflections: text("reflections", {
    mode: "json",
  }).default([]),

  // goals
  goals: text("goals", {
    mode: "json",
  }).default([]),

  created_at: text("created_at")
    .notNull()
    .default(sql`(CURRENT_TIMESTAMP)`),
});

export const submissionInsertSchema = z
  .object({
    student_id: z.string().uuid(),
    assignment_id: z.string().uuid(),
    theme: z.string().optional().default(""),
    setting: z.string().optional().default(""),
    key_plot_points: z.string().optional().default(""),
    story_text: z.string().optional().default(""),
    story_starters: z.array(
      z.object({
        label: z.string().default(""),
        input: z.string().optional().default(""),
      })
    ),
    plot_structure_scaffold: z
      .object({
        intro: z.string().default(""),
        climax: z.string().default(""),
        problem: z.string().default(""),
        escalation: z.string().default(""),
        resolution: z.string().default(""),
      })
      .optional(),
    narrative_checklist: z
      .array(
        z.object({
          label: z.string().default(""),
          is_checked: z.boolean().default(false),
        })
      )
      .optional(),
    sentence_challenges: z
      .array(
        z.object({
          label: z.string().default(""),
          input: z.string().optional().default(""),
        })
      )
      .optional(),
    thought_experiments: z
      .array(
        z.object({
          label: z.string().default(""),
          input: z.string().optional().default(""),
        })
      )
      .optional(),
    reflections: z
      .array(
        z.object({
          label: z.string().default(""),
          input: z.string().optional().default(""),
        })
      )
      .optional(),
    goals: z
      .array(
        z.object({
          label: z.string().default(""),
          is_checked: z.boolean().default(false),
        })
      )
      .optional(),
  })
  .strip();
