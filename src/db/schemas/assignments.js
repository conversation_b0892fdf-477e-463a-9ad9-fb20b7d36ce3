import { z } from "zod";
import { randomUUID } from "node:crypto";
import { integer, sqliteTable, text } from "drizzle-orm/sqlite-core";

export const assignments = sqliteTable("assignments", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => randomUUID()),

  image: text("image").default(""),

  title: text("title").default(""),
  description: text("description").default(""),

  status: text("status").default("draft"),

  word_bank: text("word_bank", { mode: "json" }).default([]),
  story_starters: text("story_starters", { mode: "json" }).default([]),
  sentence_starters: text("sentence_starters", { mode: "json" }).default([]),
  due_date: integer("due_date").$defaultFn(() => Date.now()),
});

export const assignmentInsertSchema = z.object({
  image: z.string().url().optional(),
  title: z.string().optional(),
  description: z.string().optional(),
  status: z.string().optional().default("draft"),
  word_bank: z.array(z.string()),
  story_starters: z.array(z.string()),
  sentence_starters: z.array(z.string()),
  due_date: z.number().optional(),
});
