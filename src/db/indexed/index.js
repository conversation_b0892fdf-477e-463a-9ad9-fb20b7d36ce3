import <PERSON>ie from "dexie";
import * as R from "ramda";
import * as RA from "ramda-adjunct";

export const db = new <PERSON>ie("writing-adventures");

const flattenUserInfo = R.pipe(
  R.prop("data"),
  RA.renameKeys({
    fullName: "name",
    avatarUrl: "avatar_url",
  }),
);

db.version(1).stores({
  user: "id, name, avatar_url, email, role",
});

export const saveUserLocally = async (user) => {
  const flattenedUserInfo = flattenUserInfo(user);
  await db.user.put(flattenedUserInfo);
};

export const getUserLocally = async () => {
  const user = await db.user.toArray();
  return user[0];
};
