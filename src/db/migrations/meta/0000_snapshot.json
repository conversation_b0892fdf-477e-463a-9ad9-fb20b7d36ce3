{"version": "6", "dialect": "sqlite", "id": "41d75319-3746-4f0c-92a7-1cf2e698feb0", "prevId": "00000000-0000-0000-0000-000000000000", "tables": {"assignments": {"name": "assignments", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "''"}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "''"}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "''"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'draft'"}, "word_bank": {"name": "word_bank", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'[]'"}, "story_starters": {"name": "story_starters", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'[]'"}, "sentence_starters": {"name": "sentence_starters", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'[]'"}, "due_date": {"name": "due_date", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "submissions": {"name": "submissions", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "student_id": {"name": "student_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'null'"}, "story_text": {"name": "story_text", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "''"}, "assignment_id": {"name": "assignment_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "theme": {"name": "theme", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "setting": {"name": "setting", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "key_plot_points": {"name": "key_plot_points", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "story_starters": {"name": "story_starters", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'[]'"}, "plot_structure_scaffold": {"name": "plot_structure_scaffold", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'[]'"}, "narrative_checklist": {"name": "narrative_checklist", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'[]'"}, "sentence_challenges": {"name": "sentence_challenges", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'[]'"}, "thought_experiments": {"name": "thought_experiments", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'[]'"}, "reflections": {"name": "reflections", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'[]'"}, "goals": {"name": "goals", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'[]'"}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(CURRENT_TIMESTAMP)"}}, "indexes": {}, "foreignKeys": {"submissions_assignment_id_assignments_id_fk": {"name": "submissions_assignment_id_assignments_id_fk", "tableFrom": "submissions", "tableTo": "assignments", "columnsFrom": ["assignment_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}