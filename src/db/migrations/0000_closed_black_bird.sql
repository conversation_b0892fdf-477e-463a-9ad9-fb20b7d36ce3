CREATE TABLE `assignments` (
	`id` text PRIMARY KEY NOT NULL,
	`image` text DEFAULT '',
	`title` text DEFAULT '',
	`description` text DEFAULT '',
	`status` text DEFAULT 'draft',
	`word_bank` text DEFAULT '[]',
	`story_starters` text DEFAULT '[]',
	`sentence_starters` text DEFAULT '[]',
	`due_date` integer
);
--> statement-breakpoint
CREATE TABLE `submissions` (
	`id` text PRIMARY KEY NOT NULL,
	`student_id` text DEFAULT 'null',
	`story_text` text DEFAULT '',
	`assignment_id` text,
	`theme` text DEFAULT '' NOT NULL,
	`setting` text DEFAULT '' NOT NULL,
	`key_plot_points` text DEFAULT '' NOT NULL,
	`story_starters` text DEFAULT '[]',
	`plot_structure_scaffold` text DEFAULT '[]',
	`narrative_checklist` text DEFAULT '[]',
	`sentence_challenges` text DEFAULT '[]',
	`thought_experiments` text DEFAULT '[]',
	`reflections` text DEFAULT '[]',
	`goals` text DEFAULT '[]',
	`created_at` text DEFAULT (CURRENT_TIMESTAMP) NOT NULL,
	FOREIGN KEY (`assignment_id`) REFERENCES `assignments`(`id`) ON UPDATE no action ON DELETE no action
);
