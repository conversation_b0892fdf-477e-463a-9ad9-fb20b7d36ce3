@import "tailwindcss";
@import "./utilities.css";
@plugin "tailwindcss-animate";

@theme {
  --font-primary: "<PERSON>dric<PERSON>", sans-serif;
  --color-surface-base: hsla(222, 34%, 13%, 1);
  --color-surface-layer-1: hsla(219, 33%, 14%, 1);
  --color-surface-layer-2: hsla(219, 31%, 15%, 1);
  --color-surface-layer-3: hsla(217, 19%, 27%, 1);

  --color-foreground: hsla(0, 0%, 100%, 0.7);
  --color-foreground-muted: hsla(216, 8%, 46%, 1);

  --color-border: #374151;

  --color-brand-red: hsla(1, 77%, 55%, 1);
  --color-brand-blue: hsla(217, 89%, 61%, 1);
  --color-brand-green: hsla(122, 39%, 49%, 1);
  --color-brand-yellow: hsla(46, 100%, 50%, 1);
  --color-brand-purple: hsla(291, 64%, 42%, 1);
  --color-brand-orange: hsla(36, 100%, 50%, 1);
}

::-webkit-scrollbar {
  @apply w-2;
}

::-webkit-scrollbar-track {
  @apply bg-surface-layer-1;
}

::-webkit-scrollbar-thumb {
  @apply bg-surface-layer-3 rounded;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-brand-blue;
}

.scrollbar-hidden {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

html,
body {
  font-family: var(--font-primary);
  color: var(--color-foreground);
  background-color: var(--color-surface-base);
}

input:not([type="file"]) {
  @apply input;
}

textarea {
  @apply input py-4;
}

* {
  @apply border-border/80;
  @apply outline-none;
}

.bg-brand-orange {
  background-color: var(--color-brand-orange);
}

.bg-brand-blue {
  background-color: var(--color-brand-blue);
}

.bg-brand-green {
  background-color: var(--color-brand-green);
}

.bg-brand-red {
  background-color: var(--color-brand-red);
}

.text-brand-orange {
  color: var(--color-brand-orange);
}

.text-brand-blue {
  color: var(--color-brand-blue);
}

.text-brand-green {
  color: var(--color-brand-green);
}

.text-brand-red {
  color: var(--color-brand-red);
}

.via-brand-orange {
  background-color: var(--color-brand-orange);
}

.via-brand-blue {
  background-color: var(--color-brand-blue);
}

.via-brand-green {
  background-color: var(--color-brand-green);
}

.via-brand-red {
  background-color: var(--color-brand-red);
}

@keyframes scroll-dot {
  0% {
    top: -10px;
  }
  100% {
    top: 28px;
  }
}
.animate-scroll-dot {
  animation: scroll-dot 1s infinite alternate ease-in-out;
}
