@utility button {
  @apply h-9 leading-0 flex text-sm select-none items-center gap-2 px-3 rounded-lg transition-colors cursor-pointer outline-none;
  @apply bg-brand-blue text-foreground hover:bg-brand-blue/80 active:bg-brand-blue/60 active:ring-1 active:ring-surface-layer-3;

  &.dense {
    @apply h-8 px-2 gap-1.5;
  }

  &.secondary {
    @apply bg-surface-layer-2 text-foreground-muted hover:bg-surface-layer-3/50 active:bg-surface-layer-3/70;
  }

  &.danger {
    @apply bg-brand-red text-foreground hover:bg-brand-red/80 active:bg-brand-red/60;
  }

  &.success {
    @apply bg-brand-green text-foreground hover:bg-brand-green/80 active:bg-brand-green/60;
  }

  &.warning {
    @apply bg-brand-orange text-foreground hover:bg-brand-orange/80 active:bg-brand-orange/60;
  }

  &.vibrant {
    @apply bg-brand-yellow text-black hover:bg-brand-yellow/80 active:bg-brand-yellow/60;
  }

  &.outline {
    @apply border border-border text-foreground-muted hover:bg-surface-layer-3 active:bg-surface-layer-3/50;
  }

  &.icon {
    @apply rounded-lg h-8 w-8 flex items-center justify-center p-0;
  }
}

@utility input {
  @apply bg-surface-base autofill:bg-surface-base;
  @apply focus:ring-2 focus:ring-surface-layer-3 focus:bg-surface-layer-2;
  @apply w-full min-h-12 px-3 ring-1 ring-surface-layer-3 rounded-xl transition-colors outline-none md:text-sm;

  &.dense {
    @apply min-h-8 rounded-lg px-3 scale-y-98 pt-0.5;
  }

  &.ring-highlight-primary {
    @apply focus:ring-2 focus:ring-brand-blue  focus:border-transparent;
  }

  &.ring-highlight-danger {
    @apply focus:ring-2 focus:ring-brand-red  focus:border-transparent;
  }

  &.ring-highlight-warning {
    @apply focus:ring-2 focus:ring-brand-orange  focus:border-transparent;
  }

  &.ring-highlight-success {
    @apply focus:ring-2 focus:ring-brand-green  focus:border-transparent;
  }
}

@utility nav-link {
  @apply p-4 py-3 rounded-xl;
  @apply flex items-center gap-2;
  @apply text-sm select-none leading-none transition-colors;
  @apply bg-surface-layer-2 hover:bg-surface-layer-3 active:text-foreground/80 active:bg-surface-layer-3/50  text-foreground-muted hover:text-foreground;

  &.big-pill {
    @apply h-12 items-center;
  }
}

@utility list-row {
  @apply cursor-no-drop;
  @apply border rounded-2xl;
  @apply flex-1 flex flex-row items-center;
  @apply bg-surface-layer-1  text-foreground-muted;
  @apply transition-all duration-100 text-sm gap-2 select-none pr-2;

  &.interactive {
    @apply hover:opacity-100 opacity-60 hover:bg-surface-layer-3/50 active:bg-surface-layer-3/50;
  }
}

@utility container {
  @apply max-w-screen-2xl mx-auto px-4 w-full;
}

@utility container-sm {
  @apply max-w-4xl mx-auto px-8 w-full;
}

@utility container-xs {
  @apply max-w-3xl mx-auto px-4 w-full;
}

@utility file-drop-area {
  @apply text-sm;
  @apply text-foreground-muted rounded-2xl bg-surface-base cursor-pointer h-48 border hover:bg-surface-layer-2 transition-all duration-100 p-4 flex items-center justify-center relative;
}
