<script>
  import { goto } from "$app/navigation";
  import { authenticate } from "$lib/api/lms";

  let errorMessage = $state(null);

  const login = async (e) => {
    e.preventDefault();
    errorMessage = null;
    try {
      const form = e.target;
      const email = form.email.value;
      const password = form.password.value;

      await authenticate(email, password);
      form.reset();
      goto("/writing-tasks");
    } catch (error) {
      errorMessage = error.error.message;
    }
  };
</script>

<div class="flex items-center justify-center h-screen">
  <form
    onsubmit={login}
    class="flex flex-col items-center justify-center gap-4 w-full max-w-md">
    <div class="flex flex-col items-center justify-center mb-4">
      <h1 class="text-3xl font-bold leading-relaxed">Welcome to the app</h1>
      <p class="text-lg text-gray-500">Please login to continue</p>
    </div>

    {#if errorMessage}
      <p class="text-red-500">{errorMessage}</p>
    {/if}

    <input
      name="email"
      type="email"
      placeholder="Email"
      class="border-1 rounded-lg p-2 py-3 outline-none w-full px-4 focus:ring-2 focus:ring-surface-layer-3" />
    <input
      name="password"
      type="password"
      placeholder="Password"
      class="border-1 rounded-lg p-2 py-3 outline-none w-full px-4 focus:ring-2 focus:ring-surface-layer-3" />
    <button
      class="bg-blue-500 text-white p-4 py-2 mt-4 w-full rounded-xl hover:bg-blue-600 transition-colors duration-200">
      Login
    </button>
  </form>
</div>
