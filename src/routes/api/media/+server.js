import { json } from "@sveltejs/kit";

export async function PUT({ request, platform: { env } }) {
  const formData = await request.formData();
  const file = formData.get("file");

  const name = file.name;
  await env.writing_adventures.put(name, file);

  const object = await env.writing_adventures.get(name);

  const arrayBuffer = await object.arrayBuffer();
  const base64 = Buffer.from(arrayBuffer).toString("base64");
  const url = `data:image/png;base64,${base64}`;

  return json({ url });
}

export async function GET({ request, platform: { env } }) {
  const url = new URL(request.url);
  const name = url.searchParams.get("name") || "test.png";

  const object = await env.writing_adventures.get(name);
  if (!object) {
    return json({ error: "File not found" }, 404);
  }

  const arrayBuffer = await object.arrayBuffer();
  const base64 = Buffer.from(arrayBuffer).toString("base64");
  const dataUrl = `data:image/png;base64,${base64}`;

  return json({ url: dataUrl });
}
