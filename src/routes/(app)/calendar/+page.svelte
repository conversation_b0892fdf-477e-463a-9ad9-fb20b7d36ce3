<script>
  import { Button, Calendar } from "bits-ui";
  import * as Icons from "@lucide/svelte";
  import { Dialog } from "$lib/components";
  import { getLocalTimeZone, today, fromDate } from "@internationalized/date";

  let { data } = $props();

  let value = $state(today(getLocalTimeZone()));

  let assignment = $state(null);
  let is_open = $state(false);

  const set_today = () => {
    value = today(getLocalTimeZone());
  };

  const filter_assignments = (day, month, year) => {
    return data.assignments.find((assignment) => {
      const curr_day = fromDate(
        new Date(assignment.due_date),
        "Australia/Sydney"
      );
      const curr_month = fromDate(
        new Date(assignment.due_date),
        "Australia/Sydney"
      );
      const curr_year = fromDate(
        new Date(assignment.due_date),
        "Australia/Sydney"
      );

      return (
        curr_day.day === day &&
        curr_month.month === month &&
        curr_year.year === year
      );
    });
  };

  const select_assignment = (value) => {
    assignment = value;
    is_open = true;
  };
</script>

{is_open ? "open" : "closed"}
<Calendar.Root
  class=" py-16 mx-24 rounded-2xl relative"
  weekdayFormat="short"
  type="single"
  fixedWeeks={true}
  bind:value>
  {#snippet children({ months, weekdays })}
    <div class="flex flex-row items-center justify-between space-y-4">
      <Calendar.Heading class="text-xl text-foreground select-none" />
      <Calendar.Header
        class="inline-flex items-center gap-2 justify-end mb-8  p-2  rounded-xl">
        <Calendar.PrevButton class="button icon  secondary">
          <Icons.ChevronLeft class="size-6" />
        </Calendar.PrevButton>
        <Calendar.NextButton class="button icon  secondary">
          <Icons.ChevronRight class="size-6" />
        </Calendar.NextButton>

        <Button.Root class="button secondary px-8" onclick={set_today}>
          Today
        </Button.Root>
      </Calendar.Header>
    </div>

    <div class="flex flex-col p-8 bg-surface-layer-2 rounded-2xl">
      {#each months as month, i (i)}
        <Calendar.Grid class="w-full border-collapse select-none space-y-1">
          <Calendar.GridHead>
            <Calendar.GridRow
              class="mb-1 space-x-2 flex w-full justify-between text-center">
              {#each weekdays as day, i (i)}
                <Calendar.HeadCell
                  class=" rounded-lg text-sm text-foreground  flex-1  mb-4 p-3">
                  <div class="text-center">{day.slice(0, 3)}</div>
                </Calendar.HeadCell>
              {/each}
            </Calendar.GridRow>
          </Calendar.GridHead>
          <Calendar.GridBody class="space-y-2">
            {#each month.weeks as weekDates, i (i)}
              <Calendar.GridRow class="flex w-full  rounded-lg space-x-2">
                {#each weekDates as date, i (i)}
                  {@const assignment = filter_assignments(
                    date.day,
                    date.month,
                    date.year
                  )}

                  <Calendar.Cell {date} month={month.value} class="flex-1">
                    <Calendar.Day
                      class="h-32 text-foreground-muted/50 relative pt-8   p-2 hover:border-surface-layer-3 rounded-lg data-disabled:!border-transparent  bg-surface-base data-disabled:bg-surface-layer-1/50  data-disabled:text-foreground-muted/50 data-disabled:pointer-events-none data-outside-month:pointer-events-none  data-unavailable:line-through">
                      <span class="absolute top-2 left-2">{date.day}</span>

                      {#if assignment}
                        <Button.Root
                          onclick={() => select_assignment(assignment)}
                          class="h-full w-full border rounded-lg relative overflow-hidden cursor-pointer">
                          <img
                            class="rounded-xl w-full h-full object-cover mask-b-from-5%"
                            src={assignment.image}
                            alt="assignment" />

                          <small
                            class="text-foreground block px-4 absolute bottom-2 left-0 w-full text-center overflow-hidden overflow-ellipsis">
                            {assignment.title +
                              assignment.title +
                              assignment.title}
                          </small>
                        </Button.Root>
                      {/if}
                    </Calendar.Day>
                  </Calendar.Cell>
                {/each}
              </Calendar.GridRow>
            {/each}
          </Calendar.GridBody>
        </Calendar.Grid>
      {/each}
    </div>
  {/snippet}
</Calendar.Root>

<Dialog
  size="md"
  open={is_open}
  title={assignment?.title}
  description={assignment?.description}
  onOpenChange={(e) => {
    is_open = false;
    assignment = null;
  }}
  actionButtonsProps={{
    action: {
      text: "Begin your writing journey",
      class: "vibrant",
      onclick: () => {},
    },
  }}>
  {console.log(assignment)}
  {#if assignment}
    <img
      class="rounded-xl w-full h-full object-cover"
      src={assignment.image}
      alt="assignment" />
  {/if}
</Dialog>
