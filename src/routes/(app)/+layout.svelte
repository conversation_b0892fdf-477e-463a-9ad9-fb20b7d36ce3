<script>
  import * as Icons from "@lucide/svelte";

  import { Popover } from "bits-ui";
  import { logout } from "$lib/api/lms";
  import { getContext } from "svelte";
  import { navigating, page } from "$app/state";

  import { LoadingSpinner } from "$lib/components";
  import { afterNavigate, disableScrollHandling } from "$app/navigation";

  const { children } = $props();
  const isActive = (pathname) => page.url.pathname.startsWith(pathname);

  const user = getContext("user");

  const navLinks = [
    {
      label: "Writing Tasks",
      href: "/writing-tasks",
    },
    {
      label: "Calendar",
      href: "/calendar",
    },
    {
      label: "My Writing",
      href: "/my-writing",
    },
    {
      label: "Assignments",
      href: "/assignments",
    },
  ];
  afterNavigate(disableScrollHandling);
</script>

<svelte:head>
  <title>Writing Adventure - Login to continue</title>
  <meta
    name="description"
    content="Writing Adventure is a platform for scholarly writing." />
</svelte:head>

<aside
  aria-label="sidebar"
  class="fixed left-0 top-0 h-screen flex flex-col rounded-2xl p-4 pl-8 w-[calc(16rem+2rem)] z-10">
  <div class="p-4 pl-0 pb-8">
    <p
      class="text-brand-yellow text-lg ml-2 whitespace-nowrap font-[lemon] text-shadow-sm text-shadow-black/50">
      Writing Adventures
    </p>
  </div>
  <nav class="flex-1 flex flex-col">
    <ul class="w-64 gap-2 flex flex-col flex-1 pr-4">
      {#each navLinks as link}
        <li>
          <a
            class="nav-link data-[active=true]:bg-surface-layer-3 data-[active=true]:text-foreground"
            href={link.href}
            data-active={isActive(link.href)}>
            <span class="text-sm leading-none mt-0.5">{link.label}</span>
          </a>
        </li>
      {/each}

      <li class="flex-1"></li>
      <li class="flex items-center gap-4 w-full">
        <Popover.Root>
          <Popover.Trigger
            class="flex items-center ring-1 ring-transparent cursor-pointer bg-surface-layer-2 data-[state=open]:bg-surface-layer-3/50 data-[state=open]:ring-border text-left gap-3 w-full p-3 px-3 pr-4 hover:bg-surface-layer-3/30 rounded-2xl">
            {#if user?.avatarUrl}
              <img
                src={user?.avatarUrl}
                alt="profile"
                class="size-10 rounded-full" />
            {:else}
              <div
                class="size-10 min-w-10 rounded-full bg-surface-layer-3 flex items-center justify-center">
                <Icons.User class="size-6 text-foreground-muted stroke-[1.5]" />
              </div>
            {/if}

            <div>
              <p class="text-sm text-foreground">
                {user?.fullName || "----"}
              </p>

              <p
                class="text-sm text-foreground-muted overflow-hidden text-ellipsis max-w-40">
                {user?.email}
              </p>
            </div>
          </Popover.Trigger>
          <Popover.Content
            preventScroll={true}
            class=" rounded-2xl w-54 bg-surface-layer-2 border flex flex-col gap-2 -translate-y-2 p-2">
            <button class="nav-link" onclick={logout}>
              <span class="text-sm leading-none mt-0.5">Logout</span>
            </button>

            <Popover.Arrow class="text-surface-layer-3" />
          </Popover.Content>
        </Popover.Root>
      </li>
    </ul>
  </nav>
</aside>

<div
  style="background-size: 50px;"
  class="bg-[url('/charlie-brown.svg')] bg-repeat w-screen h-screen fixed top-0 left-0 z-[0] opacity-[0.03]">
</div>

<main class="flex-1 ml-[calc(16rem+3.5rem)] z-10 bg-surface-base">
  <LoadingSpinner hidden={!navigating.to} />
  <div class:opacity-0={navigating.to}>{@render children?.()}</div>
</main>
