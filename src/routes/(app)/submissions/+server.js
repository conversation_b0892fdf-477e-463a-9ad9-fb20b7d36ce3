import { ZodError } from "zod";
import { eq } from "drizzle-orm";
import { json } from "@sveltejs/kit";
import { drizzle } from "drizzle-orm/d1";
import { fromError } from "zod-validation-error";
import { submissions, submissionInsertSchema } from "$db/schemas/submissions";

import { DrizzleQueryError } from "drizzle-orm/errors";

export const GET = async ({ platform: { env } }) => {
  // @ts-ignore
  const db = drizzle(env.DB);

  const list = await db
    .select()
    .from(submissions)
    .catch((error) => {
      console.error(error);
      return [];
    });

  return json(list || []);
};

// @ts-ignore
export const PUT = async ({ platform: { env }, request }) => {
  // @ts-ignore
  const db = drizzle(env.DB);
  const body = await request.json();

  const assignment = await db
    .update(submissions)
    .set(body)
    .where(eq(submissions.id, body.id));
  return json(assignment);
};

// @ts-ignore
export const DELETE = async ({ platform: { env }, request }) => {
  // @ts-ignore
  const db = drizzle(env.DB);
  const { id = "" } = await request.json();

  const assignment = await db
    .delete(submissions)
    .where(eq(submissions.id, id))
    .catch((error) => {
      console.error(error);
      return [];
    });
  return json(assignment);
};

// @ts-ignore

export const POST = async ({ platform: { env }, request }) => {
  // @ts-ignore
  try {
    const db = drizzle(env.DB);
    const data = await request.json();
    const body = await submissionInsertSchema.parseAsync(data);

    const newSubmission = await db
      .insert(submissions)
      .values(body)
      .onConflictDoUpdate({
        target: [submissions.id],
        set: body,
      })
      .returning();

    return json(newSubmission[0] || {});
  } catch (error) {
    if (error instanceof ZodError) {
      const zodError = fromError(error).toString();
      return json({ error: zodError }, { status: 400 });
    }

    if (error instanceof DrizzleQueryError) {
      console.log(error.cause);
    }

    return json({ error: "Internal server error" }, { status: 500 });
  }
};
