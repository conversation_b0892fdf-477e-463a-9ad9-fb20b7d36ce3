<script>
  import { state } from "../state.manager.svelte";
  import { SectionHeader, SectionInput } from "$lib/components/writing-tasks";
  import {
    challenges,
    thoughtExperiments,
    advancedSentenceStarters,
  } from "../placeholders";
</script>

<section class="container-sm mb-16">
  <SectionHeader
    name="Sentence Challenges"
    icon="Puzzle"
    description="Challenge yourself to write creative sentences."
    classes={{
      icon: "bg-brand-blue rounded-full text-white",
      title: "text-brand-blue",
    }} />

  <div
    class="p-8 bg-surface-layer-1 ring-2 flex flex-col gap-4 ring-brand-blue/20 rounded-2xl relative overflow-hidden mb-8">
    {#each challenges as challenge, idx}
      <SectionInput
        name={`Challenge ${idx + 1}`}
        description={challenge}
        bind:value={state.skill_builder.sentence_challenges[idx]}
        classes={{
          icon: "text-brand-blue bg-brand-blue/10",
          label: "text-brand-blue",
          textarea: "focus:!ring-brand-blue",
        }} />
    {/each}
  </div>
</section>

<section class="container-sm mb-16">
  <SectionHeader
    name="Stretch Your Thinking"
    icon="Brain"
    description="Push your creativity with these thought experiments"
    classes={{
      icon: "bg-brand-yellow rounded-full text-white",
      title: "text-brand-yellow",
    }} />

  <div
    class="p-8 bg-surface-layer-1 ring-2 flex flex-col gap-4 ring-brand-blue/10 rounded-2xl relative overflow-hidden mb-8">
    {#each thoughtExperiments as experiment, idx}
      <SectionInput
        bind:value={state.skill_builder.thought_experiments[idx]}
        name={`Thought Experiment ${idx + 1}`}
        description={experiment}
        classes={{
          icon: "text-brand-yellow bg-brand-yellow/10",
          label: "text-brand-yellow",
          textarea: "focus:!ring-brand-yellow",
        }} />
    {/each}
  </div>
</section>

<section class="container-sm mb-16">
  <SectionHeader
    name="Advanced Sentence Starters"
    icon="Book"
    description="Try these advanced starters to elevate your writing"
    classes={{
      icon: "bg-brand-red rounded-full text-white",
      title: "text-brand-red",
    }} />

  <div
    class="p-8 bg-surface-layer-1 ring-2 flex flex-col gap-4 ring-brand-red/20 rounded-2xl relative overflow-hidden mb-8">
    {#each advancedSentenceStarters as sentence, idx}
      <SectionInput
        name={sentence}
        bind:value={state.skill_builder.advanced_sentence_starters[idx]}
        classes={{
          icon: "text-brand-red bg-brand-red/10",
          label: "text-brand-red",
          textarea: "focus:!ring-brand-red",
        }} />
    {/each}
  </div>
</section>
