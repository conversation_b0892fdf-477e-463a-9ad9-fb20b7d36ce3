/**
 * @typedef {Object} writing_journey
 * @property {string} theme
 * @property {string} setting
 * @property {string} story_text
 * @property {string} key_plot_points
 * @property {Array<{ label: string; value: string }>} story_starters
 * @property {Array<{ label: string; value: string }>} sentence_starters
 * @property {Array<{ label: string; is_checked: boolean }>} narrative_checklist
 * @property {Array<{ label: string; value: string }>} plot_structure_scaffold
 */

/**
 * @typedef {Object} skill_builder
 * @property {Array<{ label: string; value: string }>} sentence_challenges
 * @property {Array<{ label: string; value: string }>} thought_experiments
 * @property {Array<{ label: string; value: string }>} advanced_sentence_starters
 */

/** @type {{ student_id: string; assignment_id: string; writing_journey: writing_journey; skill_builder: skill_builder; post_writing: { goals: any[]; reflections: any[]; }; }} **/

import {
  challenges,
  goals,
  narrative_list,
  plot_structure_scaffold,
  reflections,
} from "./placeholders";

export let state = $state({
  student_id: "",
  assignment_id: "",

  writing_journey: {
    theme: "",
    setting: "",
    story_text: "",
    key_plot_points: "",
    story_starters: [],
    sentence_starters: [],
    narrative_checklist: [{ label: "", is_checked: false }],
    plot_structure_scaffold: [],
  },

  skill_builder: {
    sentence_challenges: [],
    thought_experiments: [],
    advanced_sentence_starters: [],
  },

  post_writing: {
    goals: [],
    reflections: [],
  },
});

export const actions = {
  set_writing_journey: (data) => {
    state.writing_journey = { ...state.writing_journey, ...data };
  },

  set_skill_builder: (data) => {
    state.skill_builder = { ...state.skill_builder, ...data };
  },

  set_post_writing: (data) => {
    state.post_writing = { ...state.post_writing, ...data };
  },

  set_student_id: (id) => {
    state.student_id = id;
  },

  set_assignment_id: (id) => {
    state.assignment_id = id;
  },

  set_defaults: (assignment) => {
    const { story_starters, sentence_starters } = assignment;

    state.writing_journey = {
      ...state.writing_journey,

      story_starters: story_starters.map((label) => ({
        label,
        value: "",
      })),

      sentence_starters: sentence_starters.map((label) => ({
        label,
        is_checked: false,
      })),

      narrative_checklist: narrative_list.map((label) => ({
        label,
        is_checked: false,
      })),

      plot_structure_scaffold: plot_structure_scaffold.map(({ label }) => ({
        label,
        value: "",
      })),
    };

    state.skill_builder = {
      ...state.skill_builder,
      sentence_challenges: challenges.map((label) => ({
        label,
        value: "",
      })),
    };

    state.post_writing = {
      ...state.post_writing,
      reflections: reflections.map(({ label, placeholder }) => ({
        label,
        value: "",
        placeholder,
      })),

      goals: goals.map(({ label }) => ({
        label,
        is_checked: false,
      })),
    };
  },
};
