export const brain_storming_data = [
  {
    label: "Theme",
    description: "What's the central idea of message for the story?",
  },
  {
    label: "Setting",
    description:
      "Where and when does your story take place? Describe the environment and atmosphere.",
  },
  {
    label: "Key Plot Points",
    description:
      "What are the key points of the story? What are the key points of the story?",
  },
];

export const plot_structure_scaffold = [
  {
    label: "Introduction",
    description: "Introduce your main character and the setting.",
  },
  {
    label: "Problem Introduced",
    description: "What problem or challenge appears?",
  },

  {
    label: "Escalation",
    description: "How does the situation get more complicated or difficult?",
  },

  {
    label: "Climax",
    description:
      "The most exciting part! How does your character face the biggest challenge?",
  },
  {
    label: "Resolution",
    description: "How does the story end? What has changed for your character?",
  },
];

export const narrative_list = [
  "Strong opening that hooks the reader",
  "Clear main character with goals and motivations",
  "Engaging conflict or problem to solve",
  "Vivid setting descriptions",
  "Dialogue that reveals character and advances plot",
  "Satisfying resolution",
  "Character growth or change",
];

export const challenges = [
  "Write a sentence using all five senses.",
  "Write a sentence using a simile.",
  "Write a sentence that starts and ends with the same word.",
  "Create a sentence without using the letter 'e'.",
];

export const thoughtExperiments = [
  "How would your story change if it happened underwater?",
  "What if your main character had the opposite personality?",
  "How would the story be different from the villain's perspective?",
];

export const advancedSentenceStarters = [
  "Once upon a time, in a land far, far away...",
  "In a world where...",
  "What if the main character was a cat?",
];

export const reflections = [
  {
    label: "3 Things I'm Proud Of",
    placeholder: "What are 3 things you're proud of?",
  },
  {
    label: "One Thing I Could Improve",
    placeholder: "What is one thing you could improve?",
  },
  {
    label: "What I Learned About Writing",
    placeholder: "What did you learn about writing?",
  },
];

export const goals = [
  {
    label: "Clear main character with goals and motivations",
    is_checked: false,
  },
  { label: "Engaging conflict or problem to solve", is_checked: false },
  { label: "Vivid setting descriptions", is_checked: false },
];

export const scores = [
  {
    score: 50,

    label: "Ideas & Content",
    sublabel:
      "Your story has creative ideas and a clear plot. Consider adding more unique details to make it stand out.",
  },

  {
    score: 30,

    label: "Organization",
    sublabel:
      "The story is well-structured, but consider adding more subplots or backstory to keep the reader engaged.",
  },

  {
    score: 70,

    label: "Voice",
    sublabel:
      "The writing style is engaging and the voice is consistent. Consider adding more unique details to make it stand out.",
  },

  {
    score: 100,

    label: "Word Choice",
    sublabel:
      "The word choice is engaging and the voice is consistent. Consider adding more unique details to make it stand out.",
  },
];
