<script>
  import * as Icons from "@lucide/svelte";
  import { page } from "$app/state";
  import { getContext } from "svelte";
  import { actions } from "./state.manager.svelte";

  const { children, data } = $props();
  const { assignment } = data;

  const isActive = (pathname) => page.url.pathname === pathname;

  const getDate = (timestamp) => {
    if (!timestamp) return;
    const due_date = new Date(timestamp).getDate();
    const due_month = new Date(timestamp).toLocaleString("en-US", {
      month: "long",
    });
    return `${due_date} ${due_month}`;
  };

  const user = getContext("user");
  actions.set_student_id(user?.id);
</script>

<section class="relative mr-8 mt-8">
  <img
    alt="test"
    src={assignment.image}
    class="h-[93vh] mb-14 mt-4 w-full object-cover rounded-4xl mask-b-from-40%" />

  <div
    class="absolute bottom-0 left-0 w-full h-full mask-t-from-10% bg-surface-layer-1/80 flex flex-col items-center justify-center rounded-4xl">
  </div>

  <div class="absolute bottom-0 left-0 w-full">
    <div class="container-sm text-center mb-8 space-y-4">
      <button
        class="relative w-5 h-8 ring-1 overflow-hidden rounded-xl cursor-pointer ring-foreground/50 text-foreground/50 animate-pulse">
        <Icons.MoveDown
          size={16}
          fill="currentColor"
          class="absolute left-1/2 top-2 animate-scroll-dot transform -translate-x-1/2 stroke-1" />
      </button>
      <p
        class="text-brand-yellow text-sm text-center"
        class:text-brand-yellow={isActive("/writing-tasks")}
        class:text-brand-blue={isActive("/writing-tasks/skill-builder")}
        class:text-brand-green={isActive("/writing-tasks/post-writing")}>
        {getDate(assignment.due_date)}
      </p>

      <div>
        <h1 class="text-foreground text-3xl mb-2">
          {assignment.title}
        </h1>

        <p class="text-foreground-muted text-sm">
          {assignment.description}
        </p>
      </div>

      <svg
        width="155"
        height="9"
        fill="none"
        class=" text-brand-yellow m-auto scale-120 mb-12 mt-6"
        class:text-brand-yellow={isActive("/writing-tasks")}
        class:text-brand-blue={isActive("/writing-tasks/skill-builder")}
        class:text-brand-green={isActive("/writing-tasks/post-writing")}
        aria-hidden="true"
        xmlns="http://www.w3.org/2000/svg"
        ><path
          d="M1.5 4.5c5.067-4.667 10.133-4.667 15.2 0s10.133 4.667 15.2 0 10.133-4.667 15.2 0 10.133 4.667 15.2 0 10.133-4.667 15.2 0 10.133 4.667 15.2 0 10.133-4.667 15.2 0 10.133 4.667 15.2 0 10.133-4.667 15.2 0 10.133 4.667 15.2 0"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round">
        </path>
      </svg>
    </div>
  </div>
</section>

<nav class="text-center w-full my-16 container-sm sticky top-0 z-10">
  <ul
    class="flex items-center justify-start gap-2 pt-4 pb-2 px-0 bg-surface-base w-full">
    <li class="flex-1">
      <a
        href="/writing-tasks"
        data-active={isActive("/writing-tasks")}
        class="nav-link big-pill justify-center data-[active=true]:bg-brand-yellow data-[active=true]:text-black data-[active=true]:!w-full">
        <span class="text-sm leading-none mt-0.5">Writing Journey</span>
      </a>
    </li>
    <li class="flex-1">
      <a
        href="/writing-tasks/skill-builder"
        data-active={isActive("/writing-tasks/skill-builder")}
        class="nav-link big-pill justify-center data-[active=true]:bg-brand-blue data-[active=true]:text-white data-[active=true]:!w-full">
        <span class="text-sm leading-none mt-0.5">Skill Builder</span>
      </a>
    </li>
    <li class="flex-1">
      <a
        href="/writing-tasks/post-writing"
        data-active={isActive("/writing-tasks/post-writing")}
        class="nav-link big-pill justify-center data-[active=true]:bg-brand-green data-[active=true]:text-white data-[active=true]:!w-full">
        <span class="text-sm leading-none mt-0.5">Post-Writing</span>
      </a>
    </li>
  </ul>
</nav>

<div class="pb-40 relative">
  {@render children?.()}
</div>
