<script>
  import { Progress } from "bits-ui";
  import { state } from "../state.manager.svelte";
  import { reflections, scores, goals } from "../placeholders";

  import {
    Checkbox,
    SectionInput,
    SectionHeader,
  } from "$lib/components/writing-tasks";
</script>

<section class="container-sm mb-16">
  <SectionHeader
    name="Reflections"
    description="Reflect on what went well in your writing."
    icon="BookDashed"
    classes={{
      icon: "bg-brand-blue rounded-full",
      title: "text-brand-blue",
    }} />

  <div
    class="p-8 bg-surface-layer-1 ring-2 ring-brand-blue/20 rounded-2xl relative overflow-hidden mb-8">
    {#each reflections as { label, placeholder }, idx}
      <div class="mb-4">
        <SectionInput
          name={label}
          bind:value={state.post_writing.reflections[idx]}
          icon="BookDashed"
          {placeholder}
          classes={{
            icon: "text-brand-blue bg-brand-blue/10",
            label: "text-brand-blue",
            textarea: "focus:!ring-brand-blue",
          }} />
      </div>
    {/each}
  </div>
</section>

<section class="container-sm mb-16">
  <SectionHeader
    name="Feedback Rubric"
    icon="ChartBar"
    classes={{
      icon: "bg-brand-green rounded-full text-white",
      title: "text-brand-green",
    }} />
  <div
    class="p-8 bg-surface-layer-1 ring-2 ring-brand-green/20 rounded-2xl relative overflow-hidden mb-8 flex flex-col gap-4">
    {#each scores as { score, label, sublabel }}
      <div>
        <label
          for="score"
          class="text-foreground leading-loose font-bold block text-sm">
          {label}
        </label>

        <div class="flex items-center gap-4">
          <Progress.Root
            value={score}
            class="bg-surface-layer-3 shadow-mini-inset relative h-2 w-full overflow-hidden rounded-full">
            <div
              style={`transform: translateX(-${100 - (100 * (score ?? 0)) / 100}%)`}
              class="bg-brand-green gradient-to-r from-brand-green to-brand-green/10 shadow-mini-inset h-full w-full flex-1 rounded-full transition-all duration-1000 ease-in-out">
            </div>
          </Progress.Root>

          <p class="text-foreground text-sm">{score}/100</p>
        </div>

        <p class="text-foreground-muted text-sm mt-2 pr-12">
          {sublabel}
        </p>
      </div>
    {/each}
  </div>
</section>

<section class="container-sm mb-16">
  <SectionHeader
    name="Goals"
    icon="Target"
    description="Set goals for your writing journey."
    classes={{
      icon: "bg-brand-orange rounded-full text-white",
      title: "text-brand-orange",
    }} />

  <div
    class="p-8 rounded-2xl bg-surface-layer-2 border ring-1 ring-brand-orange/20 w-full relative overflow-hidden">
    <ul class="space-y-4">
      {#each goals as { label }, idx}
        <li>
          <Checkbox
            name={label}
            bind:value={state.post_writing.goals[idx]}
            classes={{
              icon: "!text-brand-orange",
              icon_unchecked: "!text-brand-orange/30",
            }} />
        </li>
      {/each}
    </ul>
  </div>
</section>
