<script>
  import * as Icons from "@lucide/svelte";
  import {
    Checkbox,
    SectionHeader,
    SectionInput,
  } from "$lib/components/writing-tasks";

  import {
    narrative_list,
    brain_storming_data,
    plot_structure_scaffold,
  } from "./placeholders";

  import { state } from "./state.manager.svelte";

  const { data } = $props();
  const assignment = data.assignment;
</script>

<section class="container-sm mb-16">
  <SectionHeader
    name="Brainstorm & Ideation"
    icon="Brain"
    classes={{
      icon: "bg-brand-yellow rounded-full !text-black",
      title: "text-brand-yellow",
    }} />

  <div
    class="p-8 bg-surface-layer-1 ring-2 ring-brand-yellow/20 rounded-2xl relative overflow-hidden mb-8">
    {#each brain_storming_data as { label, description }, idx}
      <div class="mb-4">
        <SectionInput
          text_only
          bind:value={state.writing_journey[idx]}
          name={label}
          {description}
          icon="ChartArea"
          classes={{
            icon: "text-brand-yellow bg-brand-yellow/10",
            label: "text-brand-yellow",
            textarea: "focus:!ring-brand-yellow",
          }} />
      </div>
    {/each}
  </div>
</section>

<svg
  width="155"
  height="9"
  fill="none"
  class=" text-surface-layer-3 m-auto scale-100 my-16"
  aria-hidden="true"
  xmlns="http://www.w3.org/2000/svg"
  ><path
    d="M1.5 4.5c5.067-4.667 10.133-4.667 15.2 0s10.133 4.667 15.2 0 10.133-4.667 15.2 0 10.133 4.667 15.2 0 10.133-4.667 15.2 0 10.133 4.667 15.2 0 10.133-4.667 15.2 0 10.133 4.667 15.2 0 10.133-4.667 15.2 0 10.133 4.667 15.2 0"
    stroke="currentColor"
    stroke-width="2"
    stroke-linecap="round">
  </path>
</svg>

<section class="container-sm mb-16">
  <SectionHeader
    name="Story Starters"
    icon="BookDashed"
    classes={{
      icon: "bg-brand-blue rounded-full",
      title: "text-brand-blue",
    }} />

  <div
    class="p-8 bg-surface-layer-1 ring-2 ring-brand-blue/20 rounded-2xl relative overflow-hidden mb-8">
    {#each assignment.story_starters as label, idx}
      <div class="mb-4">
        <SectionInput
          name={label}
          icon="BookDashed"
          bind:value={state.writing_journey.story_starters[idx]}
          classes={{
            icon: "text-brand-blue bg-brand-blue/10",
            label: "text-brand-blue",
            textarea: "focus:!ring-brand-blue",
          }} />
      </div>
    {/each}
  </div>
</section>

<svg
  width="155"
  height="9"
  fill="none"
  class=" text-surface-layer-3 m-auto scale-100 my-16"
  aria-hidden="true"
  xmlns="http://www.w3.org/2000/svg"
  ><path
    d="M1.5 4.5c5.067-4.667 10.133-4.667 15.2 0s10.133 4.667 15.2 0 10.133-4.667 15.2 0 10.133 4.667 15.2 0 10.133-4.667 15.2 0 10.133 4.667 15.2 0 10.133-4.667 15.2 0 10.133 4.667 15.2 0 10.133-4.667 15.2 0 10.133 4.667 15.2 0"
    stroke="currentColor"
    stroke-width="2"
    stroke-linecap="round">
  </path>
</svg>

<section class="container-sm mb-16">
  <SectionHeader
    name="Word Bank"
    icon="WholeWord"
    classes={{
      icon: "bg-violet-500 rounded-full text-white",
      title: "text-violet-500",
    }} />

  <div
    class="p-8 bg-surface-layer-1 ring-2 ring-violet-500/20 rounded-2xl relative overflow-hidden mb-8">
    <ul class="grid grid-cols-3 gap-2">
      {#each assignment.word_bank as word}
        <li
          class="p-4 rounded-xl text-sm bg-surface-layer-2 border whitespace-pre-line break-words">
          {word}
        </li>
      {/each}
    </ul>
  </div>
</section>

<svg
  width="155"
  height="9"
  fill="none"
  class=" text-surface-layer-3 m-auto scale-100 my-16"
  aria-hidden="true"
  xmlns="http://www.w3.org/2000/svg"
  ><path
    d="M1.5 4.5c5.067-4.667 10.133-4.667 15.2 0s10.133 4.667 15.2 0 10.133-4.667 15.2 0 10.133 4.667 15.2 0 10.133-4.667 15.2 0 10.133 4.667 15.2 0 10.133-4.667 15.2 0 10.133 4.667 15.2 0 10.133-4.667 15.2 0 10.133 4.667 15.2 0"
    stroke="currentColor"
    stroke-width="2"
    stroke-linecap="round">
  </path>
</svg>

<section class="container-sm mb-16">
  <SectionHeader
    name="Sentence Starters"
    icon="Text"
    classes={{
      icon: "bg-red-500 rounded-full",
      title: "text-red-500",
    }} />

  <div
    class="p-8 bg-surface-layer-1 ring-2 ring-red-500/20 rounded-2xl relative overflow-hidden mb-8">
    <ul class="space-y-4">
      {#each assignment.sentence_starters as sentence, idx}
        <li>
          <SectionInput
            bind:value={state.writing_journey.sentence_starters[idx]}
            name={sentence}
            icon="Text"
            classes={{
              icon: "text-red-500 bg-red-500/10",
              label: "text-foreground",
              textarea: "focus:!ring-red-500",
            }} />
        </li>
      {/each}
    </ul>
  </div>
</section>

<svg
  width="155"
  height="9"
  fill="none"
  class=" text-surface-layer-3 m-auto scale-100 my-16"
  aria-hidden="true"
  xmlns="http://www.w3.org/2000/svg"
  ><path
    d="M1.5 4.5c5.067-4.667 10.133-4.667 15.2 0s10.133 4.667 15.2 0 10.133-4.667 15.2 0 10.133 4.667 15.2 0 10.133-4.667 15.2 0 10.133 4.667 15.2 0 10.133-4.667 15.2 0 10.133 4.667 15.2 0 10.133-4.667 15.2 0 10.133 4.667 15.2 0"
    stroke="currentColor"
    stroke-width="2"
    stroke-linecap="round">
  </path>
</svg>

<section class="container-sm mb-16">
  <SectionHeader
    name="Plot Structure Scaffold"
    icon="LandPlot"
    classes={{
      icon: "bg-brand-green rounded-full text-white",
      title: "text-brand-green",
    }} />

  <div
    class="p-8 bg-surface-layer-1 ring-2 flex flex-col gap-4 ring-brand-green/20 rounded-2xl relative overflow-hidden mb-8">
    {#each plot_structure_scaffold as { label, description }, idx}
      <SectionInput
        name={label}
        {description}
        bind:value={state.writing_journey.plot_structure_scaffold[idx]}
        classes={{
          icon: "text-brand-green bg-brand-green/10",
          label: "text-brand-green",
          textarea: "focus:!ring-brand-green",
        }} />
    {/each}
  </div>
</section>

<svg
  width="155"
  height="9"
  fill="none"
  class=" text-surface-layer-3 m-auto scale-100 my-16"
  aria-hidden="true"
  xmlns="http://www.w3.org/2000/svg"
  ><path
    d="M1.5 4.5c5.067-4.667 10.133-4.667 15.2 0s10.133 4.667 15.2 0 10.133-4.667 15.2 0 10.133 4.667 15.2 0 10.133-4.667 15.2 0 10.133 4.667 15.2 0 10.133-4.667 15.2 0 10.133 4.667 15.2 0 10.133-4.667 15.2 0 10.133 4.667 15.2 0"
    stroke="currentColor"
    stroke-width="2"
    stroke-linecap="round">
  </path>
</svg>

<section class="container-sm mb-16">
  <SectionHeader
    name="Narrative Checklist"
    icon="Check"
    classes={{
      icon: "bg-brand-green rounded-full text-white",
      title: "text-brand-green",
    }} />

  <div
    class="p-8 rounded-2xl bg-surface-layer-2 border w-full relative overflow-hidden">
    <ul class="space-y-4">
      {#each narrative_list as name, idx}
        <li>
          <Checkbox
            classes={{
              icon_unchecked: "!text-brand-green/50",
              icon: "!text-brand-green !bg-brand-green/10",
            }}
            {name}
            bind:value={state.writing_journey.narrative_checklist[idx]} />
        </li>
      {/each}
    </ul>
  </div>
</section>

<section class="container-sm">
  <SectionHeader
    name="Final Writing Mission"
    icon="BookOpenText"
    classes={{
      icon: "bg-brand-yellow rounded-full !text-black",
      title: "text-brand-yellow",
    }} />

  <div
    class="p-8 rounded-2xl bg-surface-layer-2 border w-full relative overflow-hidden">
    <div class="flex items-start gap-2 mb-8">
      <Icons.Flame class="text-brand-yellow flex-shrink-0" size={24} />
      <p class="text-base text-foreground">
        Write a complete story about <span class="text-brand-yellow"
          >{assignment.title}</span
        >, including at least one character who discovers something unexpected.
        Use descriptive language and include dialogue.
      </p>
    </div>
    <SectionInput
      rows={16}
      placeholder="Write your story here..."
      bind:value={state.writing_journey.story_text}
      classes={{
        icon: "text-brand-yellow bg-brand-yellow/10",
        label: "text-brand-yellow",
        textarea: "focus:!ring-brand-yellow  min-h-48 resize-none",
      }} />

    <div class="flex justify-end w-full mt-8">
      <button
        class="button px-8 yellow bg-brand-yellow hover:bg-brand-yellow/90 text-black">
        Submit
      </button>
    </div>
  </div>
</section>
