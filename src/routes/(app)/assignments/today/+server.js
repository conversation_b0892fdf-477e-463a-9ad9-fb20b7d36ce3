import { between, sql } from "drizzle-orm";
import { json } from "@sveltejs/kit";
import { drizzle } from "drizzle-orm/d1";
import { assignments } from "$db/schemas/assignments";

// @ts-ignore
export const GET = async ({ platform: { env } }) => {
  // @ts-ignore
  const db = drizzle(env.DB);

  const now = new Date();
  const start = new Date(now.setHours(0, 0, 0, 0)).getTime();
  const end = new Date(now.setHours(23, 59, 59, 999)).getTime();

  const assignment = await db
    .select()
    .from(assignments)
    .where(between(assignments.due_date, start, end))
    .get()

    .catch((error) => {
      console.error(error);
      return [];
    });

  return json(assignment || {});
};
