<script>
  import * as Icons from "@lucide/svelte";
  import { state, actions } from "./state-manager.svelte";

  import {
    Dialog,
    CardList,
    Dropdown,
    DatePicker,
    LoadingSpinner,
  } from "$lib/components";

  const { data } = $props();

  actions.set_assignments(data.assignments);
</script>

<section class="space-y-6 container-sm py-24 pt-32 relative">
  <header class="flex items-center justify-between">
    <div>
      <h1 class="text-xl leading-relaxed font-bold">Assignments</h1>
      <p class="text-foreground-muted text-sm">
        Assignments are a way to create and manage tasks for your team.
      </p>
    </div>

    <button class="button" onclick={actions.open("create")}>
      <Icons.Plus size={18} />
      <span class="mt-0.5"> Create </span>
    </button>
  </header>

  <ul class="space-y-3">
    {#each state.assignments as assignment}
      {@const is_published = assignment.status === "published"}
      <li class="list-row interactive">
        <button
          class="w-full p-4 pr-0 flex flex-row items-center gap-4 cursor-pointer"
          onclick={actions.open("view", assignment)}>
          <small class="text-foreground/40 text-xs">
            {new Date(assignment.due_date).toLocaleDateString()}
          </small>

          <p class="text-foreground/80">
            {assignment.title}
          </p>

          <div class="flex-1"></div>

          <small
            class:!bg-brand-green={is_published}
            class="py-0.5 px-1 pt-1 rounded capitalize text-foreground text-xxs leading-none bg-brand-orange">
            {assignment.status}
          </small>
        </button>

        <Dropdown
          contentProps={{
            sideOffset: 4,
            class: "bg-surface-layer-1 border border-border rounded-lg",
            side: "bottom",
            align: "end",
          }}
          triggerProps={{
            class:
              "p-1 hover:bg-surface-layer-2/50 cursor-pointer rounded-lg data-[state=open]:text-foreground data-[state=open]:bg-surface-layer-3/50 ",
          }}
          itemClassName="!pr-6"
          triggerIcon="MoreVertical"
          onselect={actions.handle_action_select(assignment)}
          items={["Edit", is_published ? "Unpublish" : "Publish", "Delete"]} />
      </li>
    {/each}
  </ul>
</section>

<Dialog
  size="lg"
  title="{state.mode} Assignment"
  open={state.mode === "create" || state.mode === "edit"}
  showActionButtons={false}
  onOpenChange={actions.close}>
  {#if state.is_in_progress}
    <div
      class="absolute animate-in fade-in-0 zoom-in-95 top-0 left-0 w-full h-full bg-surface-base/60 z-10 flex items-center justify-center gap-2">
      <LoadingSpinner />
      <p class="text-foreground-muted mt-1 text-sm capitalize">
        {state.progress_text}
      </p>
    </div>
  {/if}

  <form onsubmit={actions.handleSubmit} class="space-y-6 w-full">
    <div class="space-y-2">
      {#if state.body?.image}
        <div
          class="h-48 border border-border rounded-2xl flex items-center justify-center relative">
          <button
            type="button"
            onclick={() => (state.body.image = null)}
            class="button secondary icon absolute top-2 right-2">
            <Icons.X size={16} />
          </button>

          {#if state.body?.image?.[0] instanceof File}
            <img
              alt="Cover"
              src={URL.createObjectURL(state.body.image[0])}
              class="w-full h-full object-cover rounded-2xl" />
          {:else}
            <img
              alt="Cover"
              src={state.body.image}
              class="w-full h-full object-cover rounded-2xl" />
          {/if}
        </div>
      {:else}
        <label for="cover-image" class="file-drop-area">
          <div class="flex items-center justify-center gap-2">
            <Icons.Image class="stroke-[1.2]" size={32} />
            <p class="text-foreground-muted text-sm mt-1">
              Upload a cover image
            </p>
          </div>
          <input
            max={1}
            type="file"
            disabled={state.is_in_progress}
            id="cover-image"
            bind:files={state.body.image}
            class="opacity-0 absolute top-0 left-0 w-full h-full cursor-pointer"
            accept="image/jpeg, image/png"
            placeholder="Enter your assignment cover image" />
        </label>
      {/if}
    </div>

    <div class="space-y-2">
      <label for="title" class="text-sm text-foreground-muted block">
        Title
      </label>
      <input
        required
        disabled={state.is_in_progress}
        bind:value={state.body.title}
        id="title"
        class="input"
        placeholder="Enter your assignment title" />
    </div>

    <div class="space-y-2">
      <label for="description" class="text-sm text-foreground-muted block">
        Description
      </label>
      <textarea
        required
        disabled={state.is_in_progress}
        bind:value={state.body.description}
        id="description"
        class="input"
        placeholder="Enter your assignment description"
        rows={4}
        maxlength="1500"></textarea>
    </div>

    <div class="space-y-2">
      <DatePicker bind:value={state.body.due_date} label="Due Date" />
    </div>

    <CardList
      bind:list={state.body.word_bank}
      id="word-bank"
      title="Word Bank" />

    <CardList
      id="story-starters"
      title="Story Starters"
      bind:list={state.body.story_starters} />

    <CardList
      id="sentence-starters"
      title="Sentence Starters"
      bind:list={state.body.sentence_starters} />

    <ul class="flex items-center justify-end gap-2 mt-8">
      <li>
        <button
          onclick={actions.closeCreateDialog}
          type="button"
          class="button secondary">Cancel</button>
      </li>
      <li>
        <button
          disabled={state.is_in_progress}
          type="submit"
          class="button primary">
          {state.mode === "create" ? "Create Assignment" : "Update Assignment"}
        </button>
      </li>
    </ul>
  </form>
</Dialog>

<Dialog
  size="lg"
  classes={{
    container: "!p-0",
  }}
  open={state.mode === "view"}
  showActionButtons={false}
  onOpenChange={actions.close}>
  <div class="space-y-4 relative">
    <button
      type="button"
      onclick={actions.close}
      class="button secondary icon absolute top-4 right-4 z-10 !mix-blend-difference">
      <Icons.X size={16} />
    </button>

    <img
      alt="Cover"
      src={state.body.image}
      class="w-full h-80 object-cover mask-b-from-1% bg-surface-layer-3" />

    <div class="p-8 bg-surface-layer-1 flex flex-col gap-8">
      <div>
        <div class="flex items-center justify-between rounded-2xl mb-4">
          <small
            class:bg-brand-green={state.body.status === "published"}
            class:bg-brand-orange={state.body.status === "draft"}
            class="py-0.5 px-1 pt-1 rounded capitalize text-foreground text-xxs leading-none">
            {state.body.status}
          </small>

          <p class="text-sm text-foreground-muted">
            Due Date: {state.body.due_date?.toDate().toLocaleDateString()}
          </p>
        </div>

        <div class="py-2 space-y-2">
          <h1 class="text-2xl font-bold">{state.body?.title}</h1>
          <p class="text-sm text-foreground-muted whitespace-pre-wrap">
            {state.body?.description}
          </p>
        </div>
      </div>

      <CardList
        editable={false}
        bind:list={state.body.word_bank}
        id="word-bank"
        title="Word Bank" />
      <CardList
        editable={false}
        bind:list={state.body.story_starters}
        id="story-starters"
        title="Story Starters" />

      <CardList
        editable={false}
        bind:list={state.body.sentence_starters}
        id="sentence-starters"
        title="Sentence Starters" />
    </div>
  </div>
</Dialog>

<Dialog
  size="md"
  open={state.mode === "delete"}
  title="Delete Assignment"
  description="Are you sure you want to delete this assignment? This action cannot be undone."
  actionButtonsProps={{
    cancel: {
      text: "Cancel",
    },
    action: {
      text: "Delete",
      class: "danger",
      onclick: actions.handleDeleteConfirm,
    },
  }} />
