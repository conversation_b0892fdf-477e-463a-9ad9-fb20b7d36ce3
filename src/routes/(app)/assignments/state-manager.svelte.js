import * as api from "$lib/api/assignment";

import { upload } from "$lib/api/media";
import { fromDate } from "@internationalized/date";

/**
 * @typedef {Object} assignment
 * @property {string} id
 * @property {any} image
 * @property {string} title
 * @property {string} status
 * @property {string} description
 * @property {any[]} word_bank
 * @property {any[]} story_starters
 * @property {any[]} sentence_starters
 * @property {import("@internationalized/date").ZonedDateTime | any} due_date
 */

/** @type {{ mode: string; progress_text: string; is_in_progress: boolean; assignments: assignment[]; body: assignment; }} */
export let state = $state({
  mode: "",
  progress_text: "",
  is_in_progress: false,
  assignments: [],
  body: {
    id: "",
    image: null,
    title: "",
    status: "draft",
    description: "",
    word_bank: [],
    story_starters: [],
    sentence_starters: [],
    due_date: fromDate(new Date(), "Australia/Sydney"),
  },
});

export const actions = {
  /** @type {(data: any[]) => void} */
  set_assignments: (data) => {
    state.assignments = data || [];
  },

  /** @type {(mode: string, assignment?: object) => () => void} */
  open: (mode, assignment) => () => {
    actions.reset_body();

    if (!assignment) {
      state.mode = mode;
      return;
    }

    state.body = {
      ...assignment,
      due_date: fromDate(new Date(assignment.due_date), "Australia/Sydney"),
    };

    state.mode = mode;
  },

  /** @type {() => void} */
  close: () => {
    state.mode = "";
  },

  /** @type {() => void} */
  reset_body: () => {
    state.body = {
      id: "",
      image: null,
      title: "",
      status: "draft",
      description: "",
      word_bank: [],
      story_starters: [],
      sentence_starters: [],
      due_date: fromDate(new Date(), "Australia/Sydney"),
    };
  },

  /** @type {() => Promise<void>} */
  handle_delete_confirm: async () => {
    if (!state.body?.id) return;

    await api.remove({ id: state.body.id });

    state.assignments = state.assignments.filter((a) => a.id !== state.body.id);
    actions.close();
  },

  /** @type {(assignment: object) => (action: string) => Promise<void>} */
  handle_action_select: (assignment) => async (action) => {
    if (action === "Delete") {
      actions.open("delete", assignment)();
    }

    if (action === "Edit") {
      actions.open("edit", assignment)();
    }

    if (action === "Publish") {
      await api.update(assignment.id, "published");

      state.assignments = state.assignments.map((a) =>
        a.id === assignment.id ? { ...a, status: "published" } : a
      );
    }

    if (action === "Unpublish") {
      await api.update(assignment.id, "draft");

      state.assignments = state.assignments.map((a) =>
        a.id === assignment.id ? { ...a, status: "draft" } : a
      );
    }
  },

  /** @type {(e: Event) => Promise<void>} */
  handle_submit: async (e) => {
    e.preventDefault();

    try {
      state.is_in_progress = true;

      const due_date = state.body.due_date?.toDate().getTime();
      const image = state.body?.image?.[0];

      if (!image) {
        state.is_in_progress = false;
        state.progress_text = "";
        return alert("Please upload a cover image");
      }

      state.progress_text = "Uploading image....";
      const image_url = await upload(image);

      state.progress_text = "Creating assignment....";
      const data = {
        ...state.body,
        due_date,
        image: image_url,
      };

      const assignment = await api.upsert(data);
      state.assignments = [assignment, ...state.assignments];

      actions.close();
      state.is_in_progress = false;
      state.progress_text = "";
    } catch (error) {
      console.error(error);
      state.is_in_progress = false;
    }
  },
};
