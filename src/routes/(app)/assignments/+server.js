import { eq } from "drizzle-orm";
import { json } from "@sveltejs/kit";
import { drizzle } from "drizzle-orm/d1";
import { assignmentInsertSchema, assignments } from "$db/schemas/assignments";

export const GET = async ({ platform: { env } }) => {
  // @ts-ignore
  const db = drizzle(env.DB);

  const list = await db
    .select()
    .from(assignments)
    .catch((error) => {
      console.error(error);
      return [];
    });

  return json(list || []);
};

// @ts-ignore
export const PUT = async ({ platform: { env }, request }) => {
  // @ts-ignore
  const db = drizzle(env.DB);
  const body = await request.json();

  const assignment = await db
    .update(assignments)
    .set(body)
    .where(eq(assignments.id, body.id));
  return json(assignment);
};

// @ts-ignore
export const DELETE = async ({ platform: { env }, request }) => {
  // @ts-ignore
  const db = drizzle(env.DB);
  const { id = "" } = await request.json();

  const assignment = await db
    .delete(assignments)
    .where(eq(assignments.id, id))
    .catch((error) => {
      console.error(error);
      return [];
    });
  return json(assignment);
};

// @ts-ignore

export const POST = async ({ platform: { env }, request }) => {
  // @ts-ignore
  const db = drizzle(env.DB);
  const data = await request.json();

  const body = assignmentInsertSchema.parse(data);

  const assignment = await db
    .insert(assignments)
    .values(body)
    .onConflictDoUpdate({
      target: [assignments.id],
      set: body,
    })
    .returning()
    .catch((error) => {
      console.error(error);
      return [];
    });

  return json(assignment[0] || {});
};
