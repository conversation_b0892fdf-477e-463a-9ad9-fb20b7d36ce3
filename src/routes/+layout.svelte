<script>
  import "../styles/app.css";
  import "@fontsource/aldrich";
  import "@fontsource/lemon";

  import { page } from "$app/state";
  import { setContext } from "svelte";
  import { goto } from "$app/navigation";
  import { LoadingSpinner } from "$lib/components";
  import { checkIsAuthenticated, getUserInfo } from "$lib/api/lms";

  let { children } = $props();
  let isLoading = $state(true);

  let currentUser = $state({});

  $effect(() => {
    setContext("user", currentUser);
  });

  const checkAuth = async () => {
    const auth = await checkIsAuthenticated();

    if (!auth) {
      await goto("/login", { replaceState: true, noScroll: true });
      isLoading = false;
      return;
    }

    if (page.url.pathname === "/login") {
      await goto("/writing-tasks", { replaceState: true, noScroll: true });
      isLoading = false;
      return;
    }

    const response = await getUserInfo(auth.userInfo.userId, auth.accessToken);
    currentUser = response.data;

    await goto(page.url.pathname, { replaceState: true, noScroll: true });
    isLoading = false;
  };

  checkAuth();
</script>

<svelte:head>
  <title>Writing Adventure - Login to continue</title>
  <meta
    name="description"
    content="Writing Adventure is a platform for scholarly writing." />
</svelte:head>

{#if isLoading}
  <LoadingSpinner />
{:else}
  {@render children?.()}
{/if}
