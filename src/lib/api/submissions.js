import axios from "axios";

export const submit = async (submission) => {
  const response = await axios.post("/submissions", submission);
  return response.data;
};

// export const list = async () => {
//   const response = await axios.get("/assignments");
//   return response.data;
// };

// export const update = async (id, status) => {
//   const response = await axios.put("/assignments", { id, status });
//   return response.data;
// };

// export const remove = async ({ id }) => {
//   const response = await axios.delete("/assignments", {
//     data: { id },
//   });
//   return response.data;
// };

// export const getTodays = async () => {
//   const response = await axios.get("/assignments/today");
//   return response.data;
// };
