import { goto } from "$app/navigation";

const ENDPOINT = "http://localhost:8000/v1/auth/parentOrStudent";

const authenticate = async (email, password) => {
  const response = await fetch(`${ENDPOINT}`, {
    method: "PUT",
    body: JSON.stringify({ email, password }),
    headers: {
      "Content-Type": "application/json",
    },
    mode: "cors",
    credentials: "include",
  });

  if (!response.ok) {
    const error = await response.json();
    throw error;
  }

  const data = await response.json();
  return data;
};

const getUserInfo = async (userId, token) => {
  const response = await fetch(
    `http://localhost:8000/v1/opt/getUserInfo?userId=${userId}`,
    {
      headers: {
        authorization: `Bearer ${token}`,
        "content-type": "application/json",
      },

      method: "GET",
      mode: "cors",
      credentials: "include",
    },
  );

  if (!response.ok) {
    const error = await response.json();
    throw error;
  }

  const data = await response.json();
  return data;
};

const checkIsAuthenticated = async () => {
  const response = await fetch(
    "http://localhost:8000/v1/auth/parentOrStudent",
    {
      body: null,
      method: "PATCH",
      mode: "cors",
      credentials: "include",
    },
  );

  if (!response.ok) {
    return null;
  }

  const data = await response.json();
  return data.data;
};

const logout = async () => {
  const response = await fetch(
    "http://localhost:8000/v1/auth/parentOrStudent",
    {
      method: "DELETE",
      mode: "cors",
      credentials: "include",
    },
  );

  if (!response.ok) {
    const error = await response.json();
    throw error;
  }

  await goto("/login", { replaceState: true });
};

export { authenticate, getUserInfo, checkIsAuthenticated, logout };
