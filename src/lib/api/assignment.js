import axios from "axios";

export const upsert = async (assignment) => {
  const response = await axios.post("/assignments", assignment);
  return response.data;
};

export const list = async (searchParams = "") => {
  const response = await axios.get(`/assignments?${searchParams}`);
  console.log(response.data);
  return response.data;
};

export const update = async (id, status) => {
  const response = await axios.put("/assignments", { id, status });
  return response.data;
};

export const remove = async ({ id }) => {
  const response = await axios.delete("/assignments", {
    data: { id },
  });
  return response.data;
};

export const getTodays = async () => {
  const response = await axios.get("/assignments/today");
  return response.data;
};
