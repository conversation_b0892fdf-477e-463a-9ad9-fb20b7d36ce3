<script lang="ts">
  import * as Icon from "@lucide/svelte";

  type Props = {
    id: string;
    title: string;
    list: string[];
    editable?: boolean;
    class?: string;
  };

  let {
    id,
    title,
    list = $bindable([]),
    editable = true,
    class: className,
  }: Props = $props();

  const add = (e: Event) => {
    e.preventDefault();
    const item = (e.target as HTMLFormElement).item.value;
    list = [...list, item];

    (e.target as HTMLFormElement).reset();
  };

  const remove = (item: string) => {
    list = list.filter((existingItem) => existingItem !== item);
  };
</script>

<div>
  <p class="text-sm text-foreground-muted mb-3">{title}</p>
  <ul class="border bg-surface-base rounded-2xl p-4 text-sm {className}">
    {#each list as item}
      <li class="p-2 flex items-center justify-between text-foreground">
        <p>{item}</p>

        {#if editable}
          <button
            class="text-foreground-muted cursor-pointer"
            onmousedown={() => remove(item)}>
            <Icon.X size={16} />
          </button>
        {/if}
      </li>
    {:else}
      <li>
        <p class="text-foreground-muted text-sm">Nothing yet</p>
      </li>
    {/each}

    <li>
      {#if editable}
        <form
          {id}
          name={title}
          onsubmit={add}
          class="flex items-center gap-2 mt-4">
          <input
            name="item"
            required
            class="dense"
            placeholder="Write here..." />

          <button type="submit" class="button icon secondary border">
            <Icon.Plus size={16} />
          </button>
        </form>
      {/if}
    </li>
  </ul>
</div>
