<script lang="ts">
  import { DatePicker } from "bits-ui";
  import { fromDate, ZonedDateTime } from "@internationalized/date";
  import { Calendar, ChevronRight, ChevronLeft } from "@lucide/svelte";

  type Props = {
    label: string;
    value: ZonedDateTime | null;
  };

  const placeholder = fromDate(new Date(), "Australia/Sydney");
  let { label, value = $bindable(placeholder) }: Props = $props();

  let isOpen = $state(false);
</script>

<DatePicker.Root
  bind:open={isOpen}
  required={true}
  bind:value
  hideTimeZone={true}
  weekdayFormat="short"
  fixedWeeks={true}>
  <div class="flex w-full flex-col gap-1.5">
    {#if label}
      <DatePicker.Label
        class="block select-none text-sm font-medium text-foreground-muted"
        >{label}</DatePicker.Label>
    {/if}
    <DatePicker.Input
      class="h-12 text-sm rounded-input outline-none text-foreground rounded-xl border flex items-center justify-center px-3 bg-surface-base {isOpen
        ? 'bg-surface-layer-2 ring-1 ring-border hover:border focus:text-red-500 border-transparent'
        : ''}">
      {#snippet children({ segments })}
        {#each segments as { part, value }}
          <div class="inline-block select-none">
            {#if part === "literal"}
              <DatePicker.Segment {part}>{value}</DatePicker.Segment>
            {:else}
              <DatePicker.Segment
                {part}
                class="rounded-5px outline-none focus:text-brand-orange aria-[valuetext=Empty]:text-muted-foreground focus-visible:ring-0! focus-visible:ring-offset-0! px-1 py-1">
                {value}
              </DatePicker.Segment>
            {/if}
          </div>
        {/each}
        <DatePicker.Trigger
          class="text-foreground/60 hover:bg-muted active:bg-surface-layer-3 ml-auto inline-flex size-8 items-center justify-center rounded-xl transition-all">
          <Calendar size={18} />
        </DatePicker.Trigger>
      {/snippet}
    </DatePicker.Input>
    <DatePicker.Content sideOffset={6} class="z-50 bg-surface-layer-2">
      <DatePicker.Calendar
        class="border bg-background-alt shadow-popover rounded-[15px]  p-[22px]">
        {#snippet children({ months, weekdays })}
          <DatePicker.Header class="flex items-center justify-between">
            <DatePicker.PrevButton
              class="rounded-lg text-brand-orange bg-background-alt hover:bg-muted inline-flex size-10 items-center justify-center transition-all active:scale-[0.98]">
              <ChevronLeft class="size-6" />
            </DatePicker.PrevButton>
            <DatePicker.Heading class="text-sm font-medium" />
            <DatePicker.NextButton
              class="rounded-lg text-brand-orange bg-background-alt  hover:bg-muted inline-flex size-10 items-center justify-center transition-all active:scale-[0.98]">
              <ChevronRight class="size-6" />
            </DatePicker.NextButton>
          </DatePicker.Header>
          <div
            class="flex flex-col space-y-4 pt-4 sm:flex-row sm:space-x-4 sm:space-y-0">
            {#each months as month}
              <DatePicker.Grid
                class="w-full border-collapse select-none space-y-1">
                <DatePicker.GridHead>
                  <DatePicker.GridRow class="mb-1 flex w-full justify-between">
                    {#each weekdays as day}
                      <DatePicker.HeadCell
                        class="text-brand-orange font-normal! w-10 rounded-md text-xs">
                        <div>{day.slice(0, 2)}</div>
                      </DatePicker.HeadCell>
                    {/each}
                  </DatePicker.GridRow>
                </DatePicker.GridHead>
                <DatePicker.GridBody>
                  {#each month.weeks as weekDates}
                    <DatePicker.GridRow class="flex w-full gap-1">
                      {#each weekDates as date}
                        <DatePicker.Cell
                          {date}
                          month={month.value}
                          class="p-0! relative size-9 text-center text-sm">
                          <DatePicker.Day
                            class="text-foreground rounded-xl border !border-transparent hover:!border-border cursor-pointer data-selected:bg-brand-orange data-disabled:text-foreground/30 data-selected:text-background data-unavailable:text-muted-foreground data-disabled:pointer-events-none data-outside-month:pointer-events-none data-selected:font-medium data-unavailable:line-through group relative inline-flex size-9 items-center justify-center whitespace-nowrap  p-0 text-sm font-normal transition-all">
                            <div
                              class="bg-orange-500 group-data-selected:bg-background group-data-today:block absolute top-[2px] hidden size-1 rounded-full transition-all">
                            </div>
                            {date.day}
                          </DatePicker.Day>
                        </DatePicker.Cell>
                      {/each}
                    </DatePicker.GridRow>
                  {/each}
                </DatePicker.GridBody>
              </DatePicker.Grid>
            {/each}
          </div>
        {/snippet}
      </DatePicker.Calendar>
    </DatePicker.Content>
  </div>
</DatePicker.Root>
