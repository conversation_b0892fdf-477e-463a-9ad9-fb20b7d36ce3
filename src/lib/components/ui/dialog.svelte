<script lang="ts">
  import type { Snippet } from "svelte";
  import { AlertDialog, type WithoutChild } from "bits-ui";

  type Props = AlertDialog.RootProps & {
    title?: string;
    classes?: {
      container?: string;
    };
    size?: "sm" | "md" | "lg";

    description?: string;
    children?: Snippet;
    triggerContent?: Snippet;
    showActionButtons?: boolean;
    actionButtonsProps?: {
      cancel?: {
        text?: string;
        class?: string;
        onclick?: () => void;
      };
      action: {
        type?: "submit" | "button";
        text: string;
        class?: string;
        onclick?: () => void;
      };
    };
    contentProps?: WithoutChild<AlertDialog.ContentProps>;
  };

  let {
    open = $bindable(false),
    children,
    contentProps,
    title,
    size = "md",
    classes,
    description,
    triggerContent,
    actionButtonsProps,
    showActionButtons = true,
    ...rootProps
  }: Props = $props();

  const sizeClass = {
    sm: "max-w-sm",
    md: "max-w-xl",
    lg: "max-w-3xl",
  };
</script>

<AlertDialog.Root bind:open {...rootProps}>
  <AlertDialog.Trigger>
    {#if triggerContent}
      {@render triggerContent?.()}
    {/if}
  </AlertDialog.Trigger>
  <AlertDialog.Portal>
    <AlertDialog.Overlay
      class="bg-surface-base/10 backdrop-blur-xs fixed inset-0 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95"
    />
    <AlertDialog.Content
      {...contentProps}
      class="group py-12
                data-[state=open]:animate-in
                data-[state=closed]:animate-out
                data-[state=closed]:fade-out-0
                data-[state=open]:fade-in-0
                data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 h-full flex items-start  justify-center z-10 !fixed inset-0 overflow-y-auto scrollbar-hidden"
    >
      <div
        class="bg-surface-layer-1 rounded-3xl shadow-lg p-8 border w-full flex-1 relative overflow-hidden h-auto
        
        {sizeClass[size]} {classes?.container || ''}"
      >
        {#if title || description}
          <div class="mb-8">
            <AlertDialog.Title
              class="text-lg font-bold leading-relaxed mb-2 capitalize group-data-[state=open]:opacity-100 opacity-0"
            >
              {title}
            </AlertDialog.Title>
            <AlertDialog.Description class="text-foreground-muted text-sm">
              {description}
            </AlertDialog.Description>
          </div>
        {/if}
        {@render children?.()}

        {#if showActionButtons && actionButtonsProps?.action}
          <div class="flex items-center justify-end gap-2 mt-8">
            {#if actionButtonsProps?.cancel}
              <AlertDialog.Cancel
                onclick={actionButtonsProps?.cancel?.onclick}
                class="button secondary {actionButtonsProps?.cancel?.class ||
                  ''}"
              >
                {actionButtonsProps?.cancel?.text || "Cancel"}
              </AlertDialog.Cancel>
            {/if}

            {#if actionButtonsProps?.action}
              <AlertDialog.Action
                type={actionButtonsProps?.action?.type || "button"}
                class="button {actionButtonsProps?.action?.class || ''}"
                onclick={actionButtonsProps?.action?.onclick}
                >{actionButtonsProps?.action?.text ||
                  "Confirm"}</AlertDialog.Action
              >
            {/if}
          </div>
        {/if}
      </div>
    </AlertDialog.Content>
  </AlertDialog.Portal>
</AlertDialog.Root>
