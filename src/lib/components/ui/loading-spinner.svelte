<script>
  import * as Icon from "@lucide/svelte";

  let {
    size = 24,
    message = null,
    centered = true,
    hidden = false,
    class: className = "",
  } = $props();
</script>

<div
  {hidden}
  class="flex items-center justify-center {centered
    ? 'h-screen'
    : ''} {className}">
  <div class="flex flex-col items-center gap-3">
    <Icon.Loader class="animate-spin text-brand-orange" {size} />
    {#if message}
      <p class="text-sm text-foreground/60">{message}</p>
    {/if}
  </div>
</div>
