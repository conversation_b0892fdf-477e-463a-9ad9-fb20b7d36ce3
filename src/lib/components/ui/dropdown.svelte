<script lang="ts">
    import * as Icons from "@lucide/svelte";
    import type { Snippet } from "svelte";
    import { DropdownMenu, type WithoutChild } from "bits-ui";
    type Props = DropdownMenu.RootProps & {
        items: string[];
        class?: string;
        triggerIcon?: string;
        itemClassName?: string;
        triggerContent?: Snippet;
        triggerProps?: WithoutChild<DropdownMenu.TriggerProps>;
        onselect?: (item: string, idx: number) => void;
        contentProps?: WithoutChild<DropdownMenu.ContentProps>;
    };

    let {
        open = $bindable(false),
        onselect,
        triggerContent,
        itemClassName,
        class: className,
        items,
        contentProps,
        triggerProps,
        triggerIcon = "MoreVertical",
        ...restProps
    }: Props = $props();

    const handleSelect = (item: string, idx: number) => (event: MouseEvent) => {
        event.preventDefault();
        onselect?.(item, idx);
    };

    const TriggerIcon = Icons[triggerIcon];
</script>

<DropdownMenu.Root bind:open {...restProps}>
    <DropdownMenu.Trigger {...triggerProps}>
        {#if triggerIcon}
            <TriggerIcon size={18} />
        {:else}
            {@render triggerContent?.()}
        {/if}
    </DropdownMenu.Trigger>
    <DropdownMenu.Portal>
        <DropdownMenu.Content
            {...contentProps}
            class="bg-surface-layer-1 border border-border rounded-lg overflow-hidden">
            <DropdownMenu.Group aria-label="dropdown-menu">
                {#each items as item, idx}
                    <DropdownMenu.Item textValue={item}>
                        <button
                            onclick={handleSelect(item, idx)}
                            class="text-sm text-foreground-muted hover:text-foreground py-2 px-3 w-full text-left cursor-pointer transition-all duration-100 bg-transparent hover:bg-surface-layer-3/80 active:bg-surface-layer-3/50 {itemClassName}">
                            {item}
                        </button>
                    </DropdownMenu.Item>
                {/each}
            </DropdownMenu.Group>
        </DropdownMenu.Content>
    </DropdownMenu.Portal>
</DropdownMenu.Root>
