<script lang="ts">
  import type { Snippet } from "svelte";
  import { AlertDialog, type WithoutChild } from "bits-ui";
  import * as Icons from "@lucide/svelte";

  type Props = AlertDialog.RootProps & {
    title?: string;
    description?: string;
    variant?: "danger" | "warning" | "success" | "default";
    size?: "sm" | "md" | "lg";
    classes?: {
      container?: string;
      header?: string;
      content?: string;
      actions?: string;
    };
    children?: Snippet;
    triggerContent?: Snippet;
    showActions?: boolean;
    actionProps?: {
      cancel?: {
        text?: string;
        class?: string;
        onclick?: () => void;
      };
      confirm?: {
        text?: string;
        class?: string;
        onclick?: () => void;
        loading?: boolean;
      };
    };
    contentProps?: WithoutChild<AlertDialog.ContentProps>;
  };

  let {
    open = $bindable(false),
    title = "Confirm Action",
    description = "",
    variant = "default",
    size = "md",
    classes = {},
    children,
    triggerContent,
    showActions = true,
    actionProps = {},
    contentProps,
    onOpenChange,
    ...restProps
  }: Props = $props();

  const sizeClasses = {
    sm: "max-w-md",
    md: "max-w-lg", 
    lg: "max-w-2xl"
  };

  const variantClasses = {
    danger: {
      icon: "AlertTriangle",
      iconClass: "text-brand-red bg-brand-red/10",
      confirmClass: "button danger"
    },
    warning: {
      icon: "AlertCircle", 
      iconClass: "text-brand-orange bg-brand-orange/10",
      confirmClass: "button warning"
    },
    success: {
      icon: "CheckCircle",
      iconClass: "text-brand-green bg-brand-green/10", 
      confirmClass: "button success"
    },
    default: {
      icon: "Info",
      iconClass: "text-brand-blue bg-brand-blue/10",
      confirmClass: "button"
    }
  };

  const currentVariant = variantClasses[variant];
  const Icon = Icons[currentVariant.icon];

  const handleOpenChange = (newOpen: boolean) => {
    open = newOpen;
    onOpenChange?.(newOpen);
  };

  const handleCancel = () => {
    actionProps.cancel?.onclick?.();
    open = false;
  };

  const handleConfirm = () => {
    actionProps.confirm?.onclick?.();
    if (!actionProps.confirm?.loading) {
      open = false;
    }
  };
</script>

<AlertDialog.Root bind:open onOpenChange={handleOpenChange} {...restProps}>
  {#if triggerContent}
    <AlertDialog.Trigger>
      {@render triggerContent()}
    </AlertDialog.Trigger>
  {/if}

  <AlertDialog.Portal>
    <AlertDialog.Overlay 
      class="fixed inset-0 z-50 bg-surface-base/80 backdrop-blur-sm animate-in fade-in-0" />
    
    <AlertDialog.Content 
      {...contentProps}
      class="fixed left-1/2 top-1/2 z-50 w-full {sizeClasses[size]} -translate-x-1/2 -translate-y-1/2 animate-in fade-in-0 zoom-in-95 slide-in-from-left-1/2 slide-in-from-top-[48%] duration-200 {classes.container || ''}">
      
      <div class="bg-surface-layer-1 border rounded-2xl p-8 shadow-lg {classes.content || ''}">
        <!-- Header -->
        <div class="flex items-start gap-4 mb-6 {classes.header || ''}">
          <div class="size-10 rounded-full flex items-center justify-center flex-shrink-0 {currentVariant.iconClass}">
            <Icon size={20} />
          </div>
          
          <div class="flex-1">
            <AlertDialog.Title class="text-lg font-bold text-foreground mb-2">
              {title}
            </AlertDialog.Title>
            
            {#if description}
              <AlertDialog.Description class="text-sm text-foreground-muted leading-relaxed">
                {description}
              </AlertDialog.Description>
            {/if}
          </div>
        </div>

        <!-- Custom Content -->
        {#if children}
          <div class="mb-6">
            {@render children()}
          </div>
        {/if}

        <!-- Actions -->
        {#if showActions}
          <div class="flex items-center justify-end gap-3 {classes.actions || ''}">
            <AlertDialog.Cancel>
              <button 
                onclick={handleCancel}
                class="button secondary {actionProps.cancel?.class || ''}">
                {actionProps.cancel?.text || "Cancel"}
              </button>
            </AlertDialog.Cancel>
            
            <AlertDialog.Action>
              <button 
                onclick={handleConfirm}
                disabled={actionProps.confirm?.loading}
                class="{currentVariant.confirmClass} {actionProps.confirm?.class || ''}">
                {#if actionProps.confirm?.loading}
                  <Icons.Loader class="animate-spin" size={16} />
                {/if}
                {actionProps.confirm?.text || "Confirm"}
              </button>
            </AlertDialog.Action>
          </div>
        {/if}
      </div>
    </AlertDialog.Content>
  </AlertDialog.Portal>
</AlertDialog.Root>
