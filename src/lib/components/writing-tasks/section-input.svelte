<script lang="ts">
  import * as Icons from "@lucide/svelte";

  type Value = { label: string; value: string } | string;
  type Classes = { icon: string; label: string; textarea: string };

  type Props = {
    icon?: string;
    name?: string;
    rows?: number;
    text_only?: boolean;
    description?: string;
    placeholder?: string;
    value?: Value;
    classes?: Classes;
    oninput?: (value: Value) => void;
    onchange?: (value: Value) => void;
  };

  let {
    icon,
    name = "",
    rows = 4,
    oninput,
    onchange,
    description = "",
    text_only = false,
    value = $bindable(),
    classes = { icon: "", label: "", textarea: "" },
    placeholder = "Please write your answer here...",
  }: Props = $props();

  const Icon = icon ? Icons[icon] : null;

  const createOutput = (textValue: string): Value =>
    text_only ? textValue : { label: name, value: textValue };

  const getTextValue = (val: Value | undefined): string => {
    if (!val) return "";
    return typeof val === "string" ? val : val.value || "";
  };

  const handleInput = (event: Event) => {
    const textValue = (event.target as HTMLTextAreaElement).value;
    const output = createOutput(textValue);
    value = output;
    oninput?.(output);
  };

  const handleChange = (event: Event) => {
    const textValue = (event.target as HTMLTextAreaElement).value;
    const output = createOutput(textValue);
    value = output;
    onchange?.(output);
  };
</script>

<div>
  <div class="mb-3">
    {#if name}
      <div class="flex items-start gap-2 pr-12">
        {#if Icon}
          <Icon
            size={20}
            class="{classes?.icon ?? ''} flex-shrink-0 rounded-full mt-1.5" />
        {/if}
        <div>
          <label
            for={name}
            class="{classes?.label ??
              ''} block mt-1.5 whitespace-pre-line break-words w-full">
            {name}
          </label>
          {#if description}
            <small class="text-sm text-foreground-muted">
              {description}
            </small>
          {/if}
        </div>
      </div>
    {/if}
  </div>
  <textarea
    {rows}
    {name}
    {placeholder}
    value={getTextValue(value)}
    class="{classes?.textarea ?? ''} resize-none !transition-all duration-200"
    oninput={handleInput}
    onchange={handleChange}></textarea>
</div>
