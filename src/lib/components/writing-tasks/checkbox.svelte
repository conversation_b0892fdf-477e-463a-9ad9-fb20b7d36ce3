<script lang="ts">
  type Value = { label: string; is_checked: boolean };
  type Classes = {
    icon?: string;
    icon_unchecked?: string;
  };

  type Props = {
    name?: string;
    value?: Value;
    classes?: Classes;
    onchange?: (value: Value) => void;
  };

  let {
    onchange,
    value = $bindable(),
    classes = {},
    name = "",
  }: Props = $props();

  import { Checkbox, Label } from "bits-ui";
  import * as Icons from "@lucide/svelte";

  const update = (label: string, is_checked: boolean) => {
    onchange?.({ label, is_checked });
    value = { label, is_checked } as Value;
  };
</script>

<Checkbox.Root
  id={name}
  checked={value?.is_checked}
  onCheckedChange={(checked) => update(name, checked)}
  class="cursor-pointer flex items-center gap-2">
  {#snippet children({ checked, indeterminate })}
    {#if indeterminate}
      <Icons.MinusSquare
        class="{classes.icon ?? ''} text-brand-green "
        size={20} />
    {:else if checked}
      <Icons.CheckSquareIcon
        class="{classes.icon ?? ''} text-brand-green"
        size={20} />
    {:else}
      <Icons.Square
        class="{classes.icon_unchecked ?? ''} text-foreground-muted"
        size={20} />
    {/if}

    <Label.Root
      for={name}
      class="text-sm leading-none mt-1 cursor-pointer peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-foreground-muted {checked
        ? '!text-foreground'
        : 'text-foreground-muted'}">
      {name}
    </Label.Root>
  {/snippet}
</Checkbox.Root>
