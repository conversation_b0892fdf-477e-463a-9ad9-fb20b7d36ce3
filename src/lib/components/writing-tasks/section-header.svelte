<script>
  import * as Icons from "@lucide/svelte";
  const {
    name,
    icon = "Brain",
    description = "",
    classes = { icon: "", title: "" },
  } = $props();

  const Icon = icon ? Icons[icon] : null;
</script>

<div class:items-start={description} class="flex items-center gap-4 mb-6">
  <div
    class="size-8 {classes?.icon || ''} 
      rounded-full flex items-center flex-shrink-0 justify-center text-foreground"
  >
    {#if Icon}
      <Icon size={18} />
    {/if}
  </div>
  <div>
    <h2
      class="text-lg {classes?.title ?? ''}
         font-bold capitalize leading-none mt-1.5 mb-1"
    >
      {name}
    </h2>

    {#if description}
      <small class="text-sm text-foreground-muted">
        {description}
      </small>
    {/if}
  </div>
</div>
