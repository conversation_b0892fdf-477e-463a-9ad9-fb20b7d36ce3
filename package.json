{"name": "writing-adventures", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev --port=3000", "build": "vite build", "preview": "npm run build && wrangler dev", "prepare": "svelte-kit sync || echo ''", "deploy": "npm run build && wrangler deploy", "drizzle:update": "drizzle-kit generate --out ./src/db/migrations --schema ./src/db/schemas/index.js --dialect sqlite", "d1:migrate": "wrangler d1 migrations apply dev", "check": "svelte-kit sync && svelte-check --tsconfig ./jsconfig.json"}, "devDependencies": {"@sveltejs/adapter-auto": "^6.0.0", "@sveltejs/adapter-cloudflare": "^7.0.3", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tailwindcss/vite": "^4.0.0", "better-sqlite3": "^11.10.0", "drizzle-kit": "^0.31.1", "svelte": "^5.0.0", "svelte-check": "^4.2.1", "tailwindcss": "^4.0.0", "tailwindcss-animate": "^1.0.7", "vite": "^6.2.6", "wrangler": "^4.17.0"}, "dependencies": {"@aws-sdk/client-s3": "^3.820.0", "@aws-sdk/s3-request-presigner": "^3.820.0", "@fontsource/aldrich": "^5.2.5", "@fontsource/lemon": "^5.2.5", "@lucide/svelte": "^0.511.0", "axios": "^1.9.0", "bits-ui": "^2.2.0", "dexie": "^4.0.11", "drizzle-orm": "^0.44.0", "drizzle-zod": "^0.8.2", "ramda": "^0.30.1", "ramda-adjunct": "^5.1.0", "zod": "^3.25.40", "zod-validation-error": "^3.4.1"}}