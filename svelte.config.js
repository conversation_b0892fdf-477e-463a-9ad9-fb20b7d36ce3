import adapter from "@sveltejs/adapter-cloudflare";
import { resolve } from "node:path";

/** @type {import('@sveltejs/kit').Config} */
const config = {
  kit: {
    alias: {
      $db: resolve("./src/db"),
    },
    // adapter-auto only supports some environments, see https://svelte.dev/docs/kit/adapter-auto for a list.
    // If your environment is not supported, or you settled on a specific environment, switch out the adapter.
    // See https://svelte.dev/docs/kit/adapters for more information about adapters.
    adapter: adapter(),
    csrf: {
      checkOrigin: false,
    },
  },
};

export default config;
